<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人盈亏记录与仓位计算工具</title>
    <!-- html2canvas库用于页面截图 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .main-content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.balance {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.position {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-card.winrate {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stat-title {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
        }

        .section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input, .form-group textarea {
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .records-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .records-table th,
        .records-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .records-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .profit {
            color: #28a745;
            font-weight: bold;
        }

        .loss {
            color: #dc3545;
            font-weight: bold;
        }

        .chart-container {
            height: 300px;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            padding: 20px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }

        .pagination button {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #495057;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .pagination button:hover {
            background: #e9ecef;
        }

        .pagination button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .pagination button:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.5;
        }

        .pagination .page-info {
            color: #6c757d;
            font-size: 14px;
            margin: 0 10px;
        }

        .screenshot-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .screenshot-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .screenshot-btn:active {
            transform: translateY(-1px);
        }

        /* 截图时隐藏截图按钮 */
        .hide-screenshot-btn .screenshot-btn {
            display: none !important;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 个人盈亏记录与仓位计算工具</h1>
            <p>简单易用的交易记录管理系统</p>
        </div>

        <div class="main-content">
            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card balance">
                    <div class="stat-title">当前总资金</div>
                    <div class="stat-value" id="currentBalance">1000.00 USDT</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">累计盈亏</div>
                    <div class="stat-value" id="totalProfitLoss">0.00 USDT</div>
                </div>
                <div class="stat-card position">
                    <div class="stat-title">仓位大小</div>
                    <div class="stat-value" id="positionSize">1000.00 USDT</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">建议仓位</div>
                    <div class="stat-value" id="nextPosition">50.00 USDT</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">总出金(USDT)</div>
                    <div class="stat-value" id="totalWithdrawalUSDT">0.00 USDT</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">总出金(CNY)</div>
                    <div class="stat-value" id="totalWithdrawalCNY">0.00 CNY</div>
                </div>
                <div class="stat-card winrate">
                    <div class="stat-title">交易胜率</div>
                    <div class="stat-value" id="winRate">0%</div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div class="section">
                <h2>⚙️ 系统设置</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="initialBalance">初始资金 (USDT)</label>
                        <input type="number" id="initialBalance" value="1000" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="positionPercentage">仓位百分比 (%)</label>
                        <input type="number" id="positionPercentage" value="5" step="0.1" min="0.1" max="100">
                    </div>
                </div>
                <button class="btn" onclick="saveSettings()">保存设置</button>
            </div>

            <!-- 添加交易记录 -->
            <div class="section">
                <h2>📝 添加交易记录</h2>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="recordDate">日期</label>
                        <input type="date" id="recordDate">
                    </div>
                    <div class="form-group">
                        <label for="profitLoss">盈亏 (USDT)</label>
                        <input type="number" id="profitLoss" step="0.01" placeholder="正数为盈利，负数为亏损">
                    </div>
                    <div class="form-group">
                        <label for="deposit">入金 (USDT)</label>
                        <input type="number" id="deposit" step="0.01" value="0">
                    </div>
                    <div class="form-group">
                        <label for="withdrawal">出金 (USDT)</label>
                        <input type="number" id="withdrawal" step="0.01" value="0">
                    </div>
                    <div class="form-group">
                        <label for="withdrawalCNY">出金 (CNY)</label>
                        <input type="number" id="withdrawalCNY" step="0.01" value="0">
                    </div>
                    <div class="form-group">
                        <label for="trades">交易次数</label>
                        <input type="number" id="trades" value="0" min="0">
                    </div>
                    <div class="form-group">
                        <label for="wins">胜利次数</label>
                        <input type="number" id="wins" value="0" min="0">
                    </div>
                </div>
                <button class="btn btn-success" onclick="addRecord()">添加记录</button>
            </div>

            <!-- 交易记录列表 -->
            <div class="section">
                <h2>📊 交易记录</h2>
                <div style="margin-bottom: 15px;">
                    <button class="btn" onclick="exportData()">导出数据</button>
                    <button class="btn btn-danger" onclick="clearAllData()">清空数据</button>
                </div>
                <div style="overflow-x: auto;">
                    <table class="records-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>盈亏</th>
                                <th>入金</th>
                                <th>出金</th>
                                <th>出金(CNY)</th>
                                <th>交易次数</th>
                                <th>胜利次数</th>
                                <th>胜率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="recordsTableBody">
                            <!-- 记录将在这里动态添加 -->
                        </tbody>
                    </table>
                </div>
                <!-- 分页控件 -->
                <div class="pagination" id="pagination">
                    <!-- 分页按钮将在这里动态添加 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 截图按钮 -->
    <button class="screenshot-btn" onclick="captureFullPage()" title="截图整个页面">
        📸
    </button>

    <script>
        // 数据存储
        let settings = {
            initialBalance: 1000,
            positionPercentage: 5
        };

        let records = [];
        let currentPage = 1;
        const recordsPerPage = 10;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            setTodayDate();
            updateDisplay();
        });

        // 设置今天的日期
        function setTodayDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('recordDate').value = today;
        }

        // 加载数据
        function loadData() {
            const savedSettings = localStorage.getItem('tradingSettings');
            const savedRecords = localStorage.getItem('tradingRecords');
            
            if (savedSettings) {
                settings = JSON.parse(savedSettings);
                document.getElementById('initialBalance').value = settings.initialBalance;
                document.getElementById('positionPercentage').value = settings.positionPercentage;
            }
            
            if (savedRecords) {
                records = JSON.parse(savedRecords);
            }
        }

        // 保存数据
        function saveData() {
            localStorage.setItem('tradingSettings', JSON.stringify(settings));
            localStorage.setItem('tradingRecords', JSON.stringify(records));
        }

        // 保存设置
        function saveSettings() {
            settings.initialBalance = parseFloat(document.getElementById('initialBalance').value) || 1000;
            settings.positionPercentage = parseFloat(document.getElementById('positionPercentage').value) || 5;
            
            saveData();
            updateDisplay();
            alert('设置保存成功！');
        }

        // 添加记录
        function addRecord() {
            const date = document.getElementById('recordDate').value;
            const profitLoss = parseFloat(document.getElementById('profitLoss').value) || 0;
            const deposit = parseFloat(document.getElementById('deposit').value) || 0;
            const withdrawal = parseFloat(document.getElementById('withdrawal').value) || 0;
            const withdrawalCNY = parseFloat(document.getElementById('withdrawalCNY').value) || 0;
            const trades = parseInt(document.getElementById('trades').value) || 0;
            const wins = parseInt(document.getElementById('wins').value) || 0;

            if (!date) {
                alert('请选择日期！');
                return;
            }

            if (wins > trades) {
                alert('胜利次数不能大于交易次数！');
                return;
            }

            const record = {
                id: Date.now(),
                date,
                profitLoss,
                deposit,
                withdrawal,
                withdrawalCNY,
                trades,
                wins
            };

            records.push(record);
            saveData();
            updateDisplay();
            clearForm();
            alert('记录添加成功！');
        }

        // 清空表单
        function clearForm() {
            document.getElementById('profitLoss').value = '';
            document.getElementById('deposit').value = '0';
            document.getElementById('withdrawal').value = '0';
            document.getElementById('withdrawalCNY').value = '0';
            document.getElementById('trades').value = '0';
            document.getElementById('wins').value = '0';
            setTodayDate();
        }

        // 删除记录
        function deleteRecord(id) {
            if (confirm('确定要删除这条记录吗？')) {
                records = records.filter(record => record.id !== id);
                saveData();
                // 检查当前页是否还有数据，如果没有则回到上一页
                const totalPages = Math.ceil(records.length / recordsPerPage);
                if (currentPage > totalPages && totalPages > 0) {
                    currentPage = totalPages;
                }
                updateDisplay();
            }
        }

        // 更新显示
        function updateDisplay() {
            updateStats();
            updateRecordsTable();
            updatePagination();
        }

        // 更新统计数据
        function updateStats() {
            const totalProfitLoss = records.reduce((sum, record) => sum + record.profitLoss, 0);
            const totalDeposit = records.reduce((sum, record) => sum + record.deposit, 0);
            const totalWithdrawal = records.reduce((sum, record) => sum + record.withdrawal, 0);
            const totalWithdrawalCNY = records.reduce((sum, record) => sum + record.withdrawalCNY, 0);
            const totalTrades = records.reduce((sum, record) => sum + record.trades, 0);
            const totalWins = records.reduce((sum, record) => sum + record.wins, 0);

            // 计算总盈利和总亏损
            const totalProfit = records.reduce((sum, record) => sum + (record.profitLoss > 0 ? record.profitLoss : 0), 0);
            const totalLoss = records.reduce((sum, record) => sum + (record.profitLoss < 0 ? Math.abs(record.profitLoss) : 0), 0);

            // 仓位大小计算：初始金额 + 盈利金额 - 亏损金额（不考虑出金）
            const positionSize = settings.initialBalance + totalProfit - totalLoss;

            // 当前总资金（包含出入金）
            const currentBalance = settings.initialBalance + totalProfitLoss + totalDeposit - totalWithdrawal;

            // 建议仓位基于仓位大小计算
            const nextPosition = positionSize * (settings.positionPercentage / 100);
            const winRate = totalTrades > 0 ? (totalWins / totalTrades * 100) : 0;

            document.getElementById('currentBalance').textContent = currentBalance.toFixed(2) + ' USDT';
            document.getElementById('totalProfitLoss').textContent = totalProfitLoss.toFixed(2) + ' USDT';
            document.getElementById('totalProfitLoss').className = totalProfitLoss >= 0 ? 'stat-value profit' : 'stat-value loss';
            document.getElementById('positionSize').textContent = positionSize.toFixed(2) + ' USDT';
            document.getElementById('positionSize').className = positionSize >= settings.initialBalance ? 'stat-value profit' : 'stat-value loss';
            document.getElementById('nextPosition').textContent = nextPosition.toFixed(2) + ' USDT';
            document.getElementById('totalWithdrawalUSDT').textContent = totalWithdrawal.toFixed(2) + ' USDT';
            document.getElementById('totalWithdrawalCNY').textContent = totalWithdrawalCNY.toFixed(2) + ' CNY';
            document.getElementById('winRate').textContent = winRate.toFixed(1) + '%';
        }

        // 更新记录表格
        function updateRecordsTable() {
            const tbody = document.getElementById('recordsTableBody');
            tbody.innerHTML = '';

            if (records.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="9" style="text-align: center; color: #999;">暂无交易记录</td>';
                tbody.appendChild(row);
                return;
            }

            // 按日期倒序排列
            const sortedRecords = [...records].sort((a, b) => new Date(b.date) - new Date(a.date));

            // 计算分页
            const startIndex = (currentPage - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;
            const pageRecords = sortedRecords.slice(startIndex, endIndex);

            pageRecords.forEach(record => {
                const row = document.createElement('tr');
                const winRate = record.trades > 0 ? (record.wins / record.trades * 100).toFixed(1) : '0';

                row.innerHTML = `
                    <td>${record.date}</td>
                    <td class="${record.profitLoss >= 0 ? 'profit' : 'loss'}">
                        ${record.profitLoss >= 0 ? '+' : ''}${record.profitLoss.toFixed(2)}
                    </td>
                    <td>${record.deposit.toFixed(2)}</td>
                    <td>${record.withdrawal.toFixed(2)}</td>
                    <td>${record.withdrawalCNY.toFixed(2)}</td>
                    <td>${record.trades}</td>
                    <td>${record.wins}</td>
                    <td>${winRate}%</td>
                    <td>
                        <button class="btn btn-danger" onclick="deleteRecord(${record.id})" style="padding: 5px 10px; font-size: 12px;">
                            删除
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新分页
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (records.length === 0) {
                return;
            }

            const totalPages = Math.ceil(records.length / recordsPerPage);

            if (totalPages <= 1) {
                return;
            }

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    updateDisplay();
                }
            };
            pagination.appendChild(prevBtn);

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                const firstBtn = document.createElement('button');
                firstBtn.textContent = '1';
                firstBtn.onclick = () => {
                    currentPage = 1;
                    updateDisplay();
                };
                pagination.appendChild(firstBtn);

                if (startPage > 2) {
                    const dots = document.createElement('span');
                    dots.textContent = '...';
                    dots.className = 'page-info';
                    pagination.appendChild(dots);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'active' : '';
                pageBtn.onclick = () => {
                    currentPage = i;
                    updateDisplay();
                };
                pagination.appendChild(pageBtn);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    const dots = document.createElement('span');
                    dots.textContent = '...';
                    dots.className = 'page-info';
                    pagination.appendChild(dots);
                }

                const lastBtn = document.createElement('button');
                lastBtn.textContent = totalPages;
                lastBtn.onclick = () => {
                    currentPage = totalPages;
                    updateDisplay();
                };
                pagination.appendChild(lastBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    updateDisplay();
                }
            };
            pagination.appendChild(nextBtn);

            // 页面信息
            const pageInfo = document.createElement('span');
            pageInfo.className = 'page-info';
            pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
            pagination.appendChild(pageInfo);
        }

        // 导出数据
        function exportData() {
            const data = {
                settings,
                records,
                exportDate: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `trading-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert('数据导出成功！');
        }

        // 清空所有数据
        function clearAllData() {
            if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                localStorage.removeItem('tradingSettings');
                localStorage.removeItem('tradingRecords');
                settings = { initialBalance: 1000, positionPercentage: 5 };
                records = [];
                currentPage = 1;
                document.getElementById('initialBalance').value = settings.initialBalance;
                document.getElementById('positionPercentage').value = settings.positionPercentage;
                updateDisplay();
                alert('所有数据已清空！');
            }
        }

        // 截图整个页面并复制到剪贴板
        function captureFullPage() {
            // 显示加载提示
            const originalText = document.querySelector('.screenshot-btn').innerHTML;
            document.querySelector('.screenshot-btn').innerHTML = '⏳';
            document.querySelector('.screenshot-btn').disabled = true;

            // 临时隐藏截图按钮
            document.body.classList.add('hide-screenshot-btn');

            // 获取当前滚动位置
            const originalScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // 滚动到页面顶部
            window.scrollTo(0, 0);

            // 等待滚动完成后开始截图
            setTimeout(() => {
                html2canvas(document.body, {
                    height: document.body.scrollHeight,
                    width: document.body.scrollWidth,
                    useCORS: true,
                    allowTaint: true,
                    scale: 1,
                    scrollX: 0,
                    scrollY: 0,
                    windowWidth: window.innerWidth,
                    windowHeight: window.innerHeight
                }).then(canvas => {
                    // 将canvas转换为blob
                    canvas.toBlob(blob => {
                        // 创建ClipboardItem并复制到剪贴板
                        const item = new ClipboardItem({ 'image/png': blob });
                        navigator.clipboard.write([item]).then(() => {
                            // 恢复截图按钮
                            document.body.classList.remove('hide-screenshot-btn');
                            document.querySelector('.screenshot-btn').innerHTML = originalText;
                            document.querySelector('.screenshot-btn').disabled = false;

                            // 恢复原来的滚动位置
                            window.scrollTo(0, originalScrollTop);

                            alert('页面截图已复制到剪贴板！可以直接粘贴使用。');
                        }).catch(err => {
                            console.error('复制到剪贴板失败:', err);

                            // 如果剪贴板API失败，则提供下载选项
                            const link = document.createElement('a');
                            link.download = `交易记录-${new Date().toISOString().split('T')[0]}.png`;
                            link.href = canvas.toDataURL('image/png');

                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            // 恢复截图按钮
                            document.body.classList.remove('hide-screenshot-btn');
                            document.querySelector('.screenshot-btn').innerHTML = originalText;
                            document.querySelector('.screenshot-btn').disabled = false;

                            // 恢复原来的滚动位置
                            window.scrollTo(0, originalScrollTop);

                            alert('无法复制到剪贴板，已自动下载图片文件！');
                        });
                    }, 'image/png');
                }).catch(error => {
                    console.error('截图失败:', error);

                    // 恢复截图按钮
                    document.body.classList.remove('hide-screenshot-btn');
                    document.querySelector('.screenshot-btn').innerHTML = originalText;
                    document.querySelector('.screenshot-btn').disabled = false;

                    // 恢复原来的滚动位置
                    window.scrollTo(0, originalScrollTop);

                    alert('截图失败，请重试！');
                });
            }, 100);
        }
    </script>
</body>
</html>
